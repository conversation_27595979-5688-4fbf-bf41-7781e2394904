<template>
  <div class="bg-dark text-white h-full overflow-x-hidden">
    <!-- 顶部导航栏 -->
    <header class="bg-dark-border backdrop-blur-md border-b border-dark-border sticky top-0 z-50">
      <div class="container mx-auto px-4 py-2">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-3">
          <div class="flex items-center space-x-2 w-full md:w-auto">
            <i class="fa fa-tint text-primary text-2xl"></i>
            <h1 class="text-xl md:text-2xl font-bold text-gradient">台风专题监控系统</h1>
          </div>



          <div class="flex items-center space-x-4 w-full md:w-auto justify-end">
            <div class="hidden md:flex items-center space-x-2">
              <i class="fa fa-clock-o text-secondary"></i>
              <span class="text-sm md:text-base">{{ currentTime }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <i class="fa fa-refresh text-secondary"></i>
              <span class="text-sm md:text-base">上次更新: {{ updateTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主内容区 - 单列布局 -->
    <main class="container mx-auto px-4 py-6">
      <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">

        <!-- 左侧：台风列表、台风详情、台风路径图 -->
        <div class="xl:col-span-1 space-y-4 overflow-y-auto">
          <!-- 台风列表 -->
          <TyphoonList
            :storm-list="stormList"
            :selected-storm-id="selectedStormId"
            @select-storm="selectStorm"
          />

          <!-- 台风详情 -->
          <TyphoonDetails
            :selected-storm-id="selectedStormId"
            :selected-storm-name="selectedStormName"
            :current-location="currentLocation"
            :typhoon-type="typhoonType"
            :current-pressure="currentPressure"
            :current-wind-speed="currentWindSpeed"
            :forecast12h="forecast12h"
            :forecast24h="forecast24h"
            :forecast48h="forecast48h"
            @refresh-storm-data="refreshStormData"
          />

          <!-- 台风路径图 -->
          <TyphoonPathChart
            :path-chart-option="pathChartOption"
            :chart-loading="chartLoading"
          />
        </div>

        <!-- 右侧：设备监控 -->
        <div class="xl:col-span-3 space-y-4">
          <DeviceMonitoring
            :device-data="deviceData"
            :temperature-chart-option="temperatureChartOption"
            :humidity-chart-option="humidityChartOption"
            :wind-speed-chart-option="windSpeedChartOption"
            :pressure-chart-option="pressureChartOption"
            :rainfall-chart-option="rainfallChartOption"
            :wave-chart-option="waveChartOption"
            :visibility-chart-option="visibilityChartOption"
          />
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import TyphoonList from './components/TyphoonList.vue'
import TyphoonDetails from './components/TyphoonDetails.vue'
import TyphoonPathChart from './components/TyphoonPathChart.vue'
import DeviceMonitoring from './components/DeviceMonitoring.vue'

// API 配置
const API_CONFIG = {
  stormListUrl: 'https://devapi.qweather.com/v7/tropical/storm-list',
  stormForecastUrl: 'https://devapi.qweather.com/v7/tropical/storm-forecast',
  key: '005ad0bad9f442e6ac4df7d29a73ffa5',
  basin: 'NP',
  year: '2025'
}

// 台风等级映射
const TYPHOON_TYPE_MAPPING = {
  "TD": "热带低压",
  "TS": "热带风暴",
  "STS": "强热带风暴",
  "TY": "台风",
  "STY": "强台风",
  "SuperTY": "超强台风"
}

// 响应式数据
const currentTime = ref('加载中...')
const updateTime = ref('加载中...')
const stormList = ref([])
const selectedStormId = ref(null)
const selectedStormName = ref('台风名称')
const chartLoading = ref(false)

// 台风详情数据
const currentLocation = ref('--')
const typhoonType = ref('--')
const currentPressure = ref('-- hPa')
const currentWindSpeed = ref('-- m/s')

// 预测数据
const forecast12h = reactive({ location: '--', wind: '--' })
const forecast24h = reactive({ location: '--', wind: '--' })
const forecast48h = reactive({ location: '--', wind: '--' })

// 设备数据
const deviceData = reactive({
  temperature: '26.5',
  humidity: '78',
  windSpeed: '18.2',
  windDirection: 45,
  windDirectionText: '东北偏北',
  pressure: '1002',
  rainfall: '25.8',
  waveHeight: '3.2',
  visibility: '8.5'
})

// 台风路径图表配置
const pathChartOption = ref(null)

// 设备图表配置
const temperatureChartOption = computed(() => ({
  grid: { top: 0, left: 0, right: 0, bottom: 0 },
  xAxis: { type: 'category', show: false, data: ['', '', '', '', '', '', ''] },
  yAxis: { type: 'value', show: false, min: 24, max: 28 },
  series: [{
    type: 'line',
    data: [25.2, 25.8, 26.1, 26.3, 26.5, 26.4, 26.5],
    lineStyle: { color: '#FF7D00', width: 2 },
    areaStyle: { color: 'rgba(255, 125, 0, 0.1)' },
    symbol: 'none',
    smooth: true
  }]
}))

const humidityChartOption = computed(() => ({
  grid: { top: 0, left: 0, right: 0, bottom: 0 },
  xAxis: { type: 'category', show: false, data: ['', '', '', '', '', '', ''] },
  yAxis: { type: 'value', show: false, min: 65, max: 85 },
  series: [{
    type: 'line',
    data: [72, 74, 76, 75, 77, 78, 78],
    lineStyle: { color: '#36BFFA', width: 2 },
    areaStyle: { color: 'rgba(54, 191, 250, 0.1)' },
    symbol: 'none',
    smooth: true
  }]
}))

const windSpeedChartOption = computed(() => ({
  grid: { top: 0, left: 0, right: 0, bottom: 0 },
  xAxis: { type: 'category', show: false, data: ['', '', '', '', '', '', ''] },
  yAxis: { type: 'value', show: false, min: 12, max: 22 },
  series: [{
    type: 'line',
    data: [15.2, 16.5, 17.8, 18.2, 18.5, 18.3, 18.2],
    lineStyle: { color: '#165DFF', width: 2 },
    areaStyle: { color: 'rgba(22, 93, 255, 0.1)' },
    symbol: 'none',
    smooth: true
  }]
}))

const pressureChartOption = computed(() => ({
  grid: { top: 0, left: 0, right: 0, bottom: 0 },
  xAxis: { type: 'category', show: false, data: ['', '', '', '', '', '', ''] },
  yAxis: { type: 'value', show: false, min: 1000, max: 1006 },
  series: [{
    type: 'line',
    data: [1004.2, 1003.8, 1003.5, 1003.0, 1002.5, 1002.2, 1002.0],
    lineStyle: { color: '#A855F7', width: 2 },
    areaStyle: { color: 'rgba(168, 85, 247, 0.1)' },
    symbol: 'none',
    smooth: true
  }]
}))

const rainfallChartOption = computed(() => ({
  grid: { top: 0, left: 0, right: 0, bottom: 0 },
  xAxis: { type: 'category', show: false, data: ['', '', '', '', '', '', ''] },
  yAxis: { type: 'value', show: false, min: 0, max: 30 },
  series: [{
    type: 'bar',
    data: [0, 2.5, 5.3, 8.2, 12.5, 18.7, 25.8],
    itemStyle: { color: '#3B82F6', borderRadius: [4, 4, 0, 0] },
    barWidth: '60%'
  }]
}))

const waveChartOption = computed(() => ({
  grid: { top: 0, left: 0, right: 0, bottom: 0 },
  xAxis: { type: 'category', show: false, data: ['', '', '', '', '', '', ''] },
  yAxis: { type: 'value', show: false, min: 0, max: 5 },
  series: [{
    type: 'line',
    data: [2.1, 2.8, 3.2, 3.5, 3.2, 2.9, 3.2],
    lineStyle: { color: '#06B6D4', width: 2 },
    areaStyle: { color: 'rgba(6, 182, 212, 0.1)' },
    symbol: 'none',
    smooth: true
  }]
}))

const visibilityChartOption = computed(() => ({
  grid: { top: 0, left: 0, right: 0, bottom: 0 },
  xAxis: { type: 'category', show: false, data: ['', '', '', '', '', '', ''] },
  yAxis: { type: 'value', show: false, min: 0, max: 15 },
  series: [{
    type: 'line',
    data: [6.2, 7.5, 8.1, 8.5, 9.2, 8.8, 8.5],
    lineStyle: { color: '#EAB308', width: 2 },
    areaStyle: { color: 'rgba(234, 179, 8, 0.1)' },
    symbol: 'none',
    smooth: true
  }]
}))

// 方法
const updateCurrentTime = () => {
  const now = new Date()
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
    timeZone: 'Asia/Shanghai'
  }
  currentTime.value = now.toLocaleString('zh-CN', options)
}

const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '未知'

  try {
    const date = new Date(dateTimeStr)
    if (date.toString() === 'Invalid Date') return dateTimeStr

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  } catch (error) {
    return dateTimeStr
  }
}

const showError = (message) => {
  console.error(message)
  // 这里可以集成Element Plus的消息提示
  ElMessage.error(message)
}

// 获取台风列表
const fetchStormList = async () => {
  try {
    const response = await fetch(`${API_CONFIG.stormListUrl}?basin=${API_CONFIG.basin}&year=${API_CONFIG.year}&key=${API_CONFIG.key}`)
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
    const data = await response.json()

    if (data.code === "200") {
      updateTime.value = `上次更新: ${formatDateTime(data.updateTime)}`

      // 过滤活跃台风
      const activeStorms = data.storm.filter(storm => storm.isActive === "1")
      stormList.value = activeStorms

      // 如果有活跃台风，默认选中第一个
      if (activeStorms.length > 0) {
        selectStorm(activeStorms[0].id)
      }
    } else {
      showError(`获取台风列表失败: ${data.code}`)
    }
  } catch (error) {
    showError(`获取台风列表失败: ${error.message}`)
  }
}

// 获取台风预测数据
const fetchStormForecast = async (stormId) => {
  try {
    chartLoading.value = true

    const response = await fetch(`${API_CONFIG.stormForecastUrl}?stormid=${stormId}&key=${API_CONFIG.key}`)
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
    const data = await response.json()

    if (data.code === "200") {
      updateTime.value = `上次更新: ${formatDateTime(data.updateTime)}`

      // 渲染台风详情
      renderStormDetails(data.forecast)

      // 渲染台风路径图
      renderTyphoonPathChart(data.forecast)
    } else {
      showError(`获取台风详情失败: ${data.code}`)
    }
  } catch (error) {
    showError(`获取台风详情失败: ${error.message}`)
  } finally {
    chartLoading.value = false
  }
}

// 选择台风
const selectStorm = (stormId) => {
  if (selectedStormId.value === stormId) return

  selectedStormId.value = stormId

  // 获取台风详情
  const selectedStorm = stormList.value.find(storm => storm.id === stormId)
  if (selectedStorm) {
    selectedStormName.value = selectedStorm.name
    fetchStormForecast(stormId)
  } else {
    showError('未找到选中的台风信息')
  }
}

// 渲染台风详情
const renderStormDetails = (forecast) => {
  if (!forecast || forecast.length === 0) {
    currentLocation.value = '--'
    currentPressure.value = '-- hPa'
    currentWindSpeed.value = '-- m/s'
    typhoonType.value = '--'

    forecast12h.location = '--'
    forecast12h.wind = '--'
    forecast24h.location = '--'
    forecast24h.wind = '--'
    forecast48h.location = '--'
    forecast48h.wind = '--'

    return
  }

  try {
    const current = forecast[0]

    // 更新当前数据
    currentLocation.value = current.lat && current.lon
      ? `${current.lat}°N, ${current.lon}°E`
      : '--'

    currentPressure.value = current.pressure
      ? `${current.pressure} hPa`
      : '-- hPa'

    currentWindSpeed.value = current.windSpeed
      ? `${current.windSpeed} m/s`
      : '-- m/s'

    typhoonType.value = current.type
      ? (TYPHOON_TYPE_MAPPING[current.type] || current.type)
      : '--'

    // 更新预测数据
    if (forecast.length > 1) {
      const forecast12hData = forecast[1]
      forecast12h.location = forecast12hData.lat && forecast12hData.lon
        ? `${forecast12hData.lat}°N, ${forecast12hData.lon}°E`
        : '--'
      forecast12h.wind = forecast12hData.windSpeed || '--'
    }

    if (forecast.length > 2) {
      const forecast24hData = forecast[2]
      forecast24h.location = forecast24hData.lat && forecast24hData.lon
        ? `${forecast24hData.lat}°N, ${forecast24hData.lon}°E`
        : '--'
      forecast24h.wind = forecast24hData.windSpeed || '--'
    }

    if (forecast.length > 4) {
      const forecast48hData = forecast[4]
      forecast48h.location = forecast48hData.lat && forecast48hData.lon
        ? `${forecast48hData.lat}°N, ${forecast48hData.lon}°E`
        : '--'
      forecast48h.wind = forecast48hData.windSpeed || '--'
    }
  } catch (error) {
    showError(`渲染台风详情时出错: ${error.message}`)
    console.error('渲染台风详情错误:', error)
  }
}

// 渲染台风路径图
const renderTyphoonPathChart = (forecast) => {
  try {
    if (!forecast || forecast.length === 0) {
      console.warn('没有可用的台风路径数据')
      pathChartOption.value = null
      return
    }

    // 提取经纬度数据
    const lats = forecast.map(point => parseFloat(point.lat) || 0)
    const lons = forecast.map(point => parseFloat(point.lon) || 0)

    // 计算经纬度范围
    const minLat = Math.min(...lats) - 1
    const maxLat = Math.max(...lats) + 1
    const minLon = Math.min(...lons) - 1
    const maxLon = Math.max(...lons) + 1

    // 创建图表配置
    pathChartOption.value = {
      backgroundColor: 'transparent',
      grid: {
        left: '15%',
        right: '15%',
        top: '10%',
        bottom: '10%'
      },
      xAxis: {
        type: 'value',
        name: '经度(°E)',
        nameTextStyle: { color: 'rgba(255, 255, 255, 0.7)' },
        min: minLon,
        max: maxLon,
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        axisTick: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.7)' },
        splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } }
      },
      yAxis: {
        type: 'value',
        name: '纬度(°N)',
        nameTextStyle: { color: 'rgba(255, 255, 255, 0.7)' },
        min: minLat,
        max: maxLat,
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        axisTick: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.7)' },
        splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#165DFF',
        textStyle: { color: '#fff' },
        formatter: (params) => {
          const point = forecast[params.dataIndex]
          if (!point) return ''

          const date = new Date(point.fxTime || '')
          const timeStr = date.toString() !== 'Invalid Date'
            ? `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:00`
            : '时间未知'

          return [
            `${timeStr}`,
            `位置: ${point.lat || '--'}°N, ${point.lon || '--'}°E`,
            `气压: ${point.pressure || '--'} hPa`,
            `风速: ${point.windSpeed || '--'} m/s`,
            `等级: ${point.type ? (TYPHOON_TYPE_MAPPING[point.type] || point.type) : '--'}`
          ].join('<br/>')
        }
      },
      series: [{
        type: 'line',
        data: forecast.map(point => [
          parseFloat(point.lon) || 0,
          parseFloat(point.lat) || 0
        ]),
        lineStyle: {
          color: '#165DFF',
          width: 3
        },
        itemStyle: {
          color: (params) => {
            return params.dataIndex === 0 ? '#F53F3F' : '#165DFF'
          },
          borderColor: '#fff',
          borderWidth: 2
        },
        symbol: 'circle',
        symbolSize: (_, params) => {
          return params.dataIndex === 0 ? 12 : 8
        },
        markPoint: {
          data: [{
            coord: [parseFloat(forecast[0].lon) || 0, parseFloat(forecast[0].lat) || 0],
            symbol: 'pin',
            symbolSize: 50,
            itemStyle: { color: '#F53F3F' },
            label: {
              show: true,
              position: 'top',
              formatter: '当前位置',
              color: '#fff',
              fontSize: 12
            }
          }]
        }
      }]
    }
  } catch (error) {
    showError(`渲染台风路径图时出错: ${error.message}`)
    console.error('渲染台风路径图错误:', error)
  }
}

// 刷新台风数据
const refreshStormData = () => {
  if (selectedStormId.value) {
    fetchStormForecast(selectedStormId.value)
  } else {
    fetchStormList()
  }
}

// 模拟设备数据更新
const simulateDeviceData = () => {
  // 随机风向 (0-360度)
  const windDirection = Math.floor(Math.random() * 360)
  deviceData.windDirection = windDirection

  // 更新风向文本
  const windDirections = ['北', '东北偏北', '东北', '东北偏东', '东', '东南偏东', '东南', '东南偏南', '南', '西南偏南', '西南', '西南偏西', '西', '西北偏西', '西北', '西北偏北']
  const directionIndex = Math.floor((windDirection % 360) / 22.5)
  deviceData.windDirectionText = windDirections[directionIndex] || '未知'

  // 随机更新其他设备数据
  deviceData.temperature = (25 + Math.random() * 3).toFixed(1)
  deviceData.humidity = Math.floor(70 + Math.random() * 15).toString()
  deviceData.windSpeed = (15 + Math.random() * 8).toFixed(1)
  deviceData.pressure = (1000 + Math.random() * 5).toFixed(1)
  deviceData.rainfall = (20 + Math.random() * 15).toFixed(1)
  deviceData.waveHeight = (2 + Math.random() * 3).toFixed(1)
  deviceData.visibility = (5 + Math.random() * 8).toFixed(1)
}

// 定时器
let timeInterval = null
let deviceInterval = null

// 生命周期
onMounted(() => {
  // 更新当前时间
  updateCurrentTime()
  timeInterval = setInterval(updateCurrentTime, 1000)

  // 获取台风列表
  fetchStormList()

  // 模拟设备数据更新
  simulateDeviceData()
  deviceInterval = setInterval(simulateDeviceData, 5000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  if (deviceInterval) {
    clearInterval(deviceInterval)
  }
})
</script>

<style scoped>
/* Tailwind自定义样式 */
.bg-dark {
  background-color: #0D1117;
}

.bg-dark-card {
  background-color: #161B22;
}

.bg-dark-border {
  background-color: #30363D;
}

.border-dark-border {
  border-color: #30363D;
}

.text-primary {
  color: #165DFF;
}

.text-secondary {
  color: #36BFFA;
}

.text-warning {
  color: #FF7D00;
}

.text-danger {
  color: #F53F3F;
}

.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(135deg, #165DFF, #36BFFA);
}

.card-shadow {
  box-shadow: 0 0 15px rgba(22, 93, 255, 0.1);
}

.typhoon-active {
  border-left: 4px solid #F53F3F !important;
  background-color: rgba(22, 27, 34, 0.8) !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(22, 93, 255, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(22, 93, 255, 0.7);
}

/* 动画效果 */
.typhoon-item {
  transition: all 0.3s ease;
}

.typhoon-item:hover {
  transform: translateY(-2px);
}

/* 响应式调整 */
@media (max-width: 1280px) {
  .xl\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .xl\:col-span-3 {
    grid-column: span 1 / span 1;
  }

  /* 在小屏幕上调整高度 */
  .space-y-4 > * {
    margin-bottom: 1rem;
  }
}

/* 确保图表容器正确显示 */
.echarts {
  width: 100% !important;
  height: 100% !important;
}
</style>