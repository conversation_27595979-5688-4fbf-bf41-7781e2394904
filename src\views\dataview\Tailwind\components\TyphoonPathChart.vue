<template>
  <div class="bg-dark-card border border-dark-border rounded-xl p-4 card-shadow h-80">
    <div class="flex justify-between items-center mb-3">
      <h2 class="text-lg font-semibold flex items-center">
        <i class="fa fa-map-o text-primary mr-2"></i>台风路径预测
      </h2>
    </div>
    <div class="h-[calc(100%-60px)] relative">
      <v-chart
        v-if="pathChartOption"
        :option="pathChartOption"
        :loading="chartLoading"
        autoresize
        class="w-full h-full"
      />
      <div v-else class="absolute inset-0 flex items-center justify-center bg-dark/70 rounded-lg">
        <i class="fa fa-spinner fa-spin text-2xl text-primary"></i>
      </div>
    </div>
  </div>
</template>

<script setup>
import VChart from 'vue-echarts'
import 'echarts'

// Props
const props = defineProps({
  pathChartOption: {
    type: Object,
    default: null
  },
  chartLoading: {
    type: Boolean,
    default: false
  }
})
</script>

<style scoped>
/* Tailwind自定义样式 */
.bg-dark {
  background-color: #0D1117;
}

.bg-dark-card {
  background-color: #161B22;
}

.bg-dark-border {
  background-color: #30363D;
}

.border-dark-border {
  border-color: #30363D;
}

.text-primary {
  color: #165DFF;
}

.card-shadow {
  box-shadow: 0 0 15px rgba(22, 93, 255, 0.1);
}

/* 确保图表容器正确显示 */
.echarts {
  width: 100% !important;
  height: 100% !important;
}
</style>
